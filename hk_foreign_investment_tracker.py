#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港外国投资追踪器
尝试从多个渠道获取真正的外资流入香港数据
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import logging
import os
import akshare as ak

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HKForeignInvestmentTracker:
    """香港外国投资追踪器"""
    
    def __init__(self):
        """初始化"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9'
        }
    
    def get_hk_international_securities_data(self):
        """
        尝试获取香港国际证券数据
        """
        try:
            logger.info("正在尝试获取香港国际证券投资数据...")
            
            # 尝试使用akshare获取相关的国际投资数据
            try:
                # 获取QDII基金数据（可能包含投资香港的基金）
                qdii_data = ak.fund_em_open_fund_info(fund="QDII", indicator="单位净值走势")
                if qdii_data is not None and not qdii_data.empty:
                    logger.info(f"获取到QDII基金数据: {len(qdii_data)} 条记录")
                    return qdii_data
            except Exception as e:
                logger.warning(f"获取QDII数据失败: {str(e)}")
            
            return None
            
        except Exception as e:
            logger.error(f"获取国际证券数据时出错: {str(e)}")
            return None
    
    def get_hk_adr_trading_data(self):
        """
        获取香港ADR交易数据
        """
        try:
            logger.info("正在尝试获取香港ADR交易数据...")
            
            # 香港主要公司的ADR代码
            hk_adrs = [
                "BABA",   # 阿里巴巴
                "BIDU",   # 百度
                "JD",     # 京东
                "NTES",   # 网易
                "TME",    # 腾讯音乐
                "BILI",   # 哔哩哔哩
            ]
            
            adr_data = []
            for symbol in hk_adrs:
                try:
                    # 尝试获取ADR股票数据
                    stock_data = ak.stock_us_daily(symbol=symbol)
                    if stock_data is not None and not stock_data.empty:
                        latest = stock_data.tail(1).iloc[0]
                        adr_data.append({
                            "symbol": symbol,
                            "date": latest.name,
                            "close": latest["close"],
                            "volume": latest["volume"]
                        })
                        logger.info(f"获取到 {symbol} ADR数据")
                except Exception as e:
                    logger.warning(f"获取 {symbol} ADR数据失败: {str(e)}")
                    continue
            
            if adr_data:
                return pd.DataFrame(adr_data)
            
            return None
            
        except Exception as e:
            logger.error(f"获取ADR数据时出错: {str(e)}")
            return None
    
    def get_hk_etf_foreign_flows(self):
        """
        获取香港相关ETF的外资流向
        """
        try:
            logger.info("正在尝试获取香港ETF外资流向数据...")
            
            # 主要的香港相关ETF代码
            hk_etfs = [
                "EWH",    # iShares MSCI Hong Kong ETF
                "FHK",    # First Trust Hong Kong AlphaDEX Fund
                "FLHK",   # Franklin FTSE Hong Kong ETF
            ]
            
            etf_data = []
            for symbol in hk_etfs:
                try:
                    # 尝试获取ETF数据
                    etf_info = ak.stock_us_daily(symbol=symbol)
                    if etf_info is not None and not etf_info.empty:
                        latest = etf_info.tail(1).iloc[0]
                        etf_data.append({
                            "etf_symbol": symbol,
                            "date": latest.name,
                            "close": latest["close"],
                            "volume": latest["volume"]
                        })
                        logger.info(f"获取到 {symbol} ETF数据")
                except Exception as e:
                    logger.warning(f"获取 {symbol} ETF数据失败: {str(e)}")
                    continue
            
            if etf_data:
                return pd.DataFrame(etf_data)
            
            return None
            
        except Exception as e:
            logger.error(f"获取ETF数据时出错: {str(e)}")
            return None
    
    def get_hk_reit_foreign_investment(self):
        """
        获取香港REIT的外国投资数据
        """
        try:
            logger.info("正在尝试获取香港REIT外国投资数据...")
            
            # 这里可以尝试获取香港主要REIT的数据
            # 由于缺乏直接API，返回None
            return None
            
        except Exception as e:
            logger.error(f"获取REIT数据时出错: {str(e)}")
            return None
    
    def get_offshore_rmb_data(self):
        """
        获取离岸人民币数据（可能反映外资对香港的兴趣）
        """
        try:
            logger.info("正在获取离岸人民币数据...")
            
            # 尝试获取离岸人民币汇率
            try:
                cnh_data = ak.currency_boc_sina(symbol="人民币", start_date="20240101", end_date="20241231")
                if cnh_data is not None and not cnh_data.empty:
                    logger.info(f"获取到离岸人民币数据: {len(cnh_data)} 条记录")
                    return cnh_data
            except Exception as e:
                logger.warning(f"获取离岸人民币数据失败: {str(e)}")
            
            return None
            
        except Exception as e:
            logger.error(f"获取离岸人民币数据时出错: {str(e)}")
            return None
    
    def analyze_foreign_investment_indicators(self):
        """
        分析外国投资指标
        """
        logger.info("开始分析外国投资指标...")
        
        results = {
            "分析时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "数据源": {},
            "分析结果": {},
            "建议": []
        }
        
        # 1. 国际证券数据
        securities_data = self.get_hk_international_securities_data()
        if securities_data is not None:
            results["数据源"]["国际证券投资"] = {
                "状态": "成功",
                "记录数": len(securities_data),
                "类型": "QDII基金等国际投资工具"
            }
        else:
            results["数据源"]["国际证券投资"] = {"状态": "失败"}
        
        # 2. ADR交易数据
        adr_data = self.get_hk_adr_trading_data()
        if adr_data is not None:
            results["数据源"]["香港ADR交易"] = {
                "状态": "成功",
                "记录数": len(adr_data),
                "说明": "香港公司在美国的ADR交易可反映国际投资者兴趣"
            }
            
            # 分析ADR数据
            total_volume = adr_data['volume'].sum()
            avg_price = adr_data['close'].mean()
            results["分析结果"]["ADR分析"] = {
                "总交易量": int(total_volume),
                "平均价格": round(float(avg_price), 2),
                "活跃ADR数量": len(adr_data)
            }
        else:
            results["数据源"]["香港ADR交易"] = {"状态": "失败"}
        
        # 3. ETF流向数据
        etf_data = self.get_hk_etf_foreign_flows()
        if etf_data is not None:
            results["数据源"]["香港ETF"] = {
                "状态": "成功",
                "记录数": len(etf_data),
                "说明": "香港相关ETF的交易活动反映外资配置需求"
            }
            
            # 分析ETF数据
            total_etf_volume = etf_data['volume'].sum()
            results["分析结果"]["ETF分析"] = {
                "总交易量": int(total_etf_volume),
                "活跃ETF数量": len(etf_data)
            }
        else:
            results["数据源"]["香港ETF"] = {"状态": "失败"}
        
        # 4. 离岸人民币数据
        cnh_data = self.get_offshore_rmb_data()
        if cnh_data is not None:
            results["数据源"]["离岸人民币"] = {
                "状态": "成功",
                "记录数": len(cnh_data),
                "说明": "离岸人民币汇率变化可间接反映资金流向"
            }
        else:
            results["数据源"]["离岸人民币"] = {"状态": "失败"}
        
        # 添加建议
        results["建议"] = [
            "ADR交易量可作为国际投资者对香港上市公司兴趣的指标",
            "香港相关ETF的资金流向能反映机构投资者的配置偏好",
            "离岸人民币汇率变化可间接反映跨境资金流动",
            "建议结合多个指标综合分析外资流向趋势",
            "获取真正的外资数据需要专业数据服务商或官方统计"
        ]
        
        return results
    
    def save_analysis_results(self, results):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hk_foreign_investment_analysis_{timestamp}.json"
        filepath = os.path.join(self.data_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"分析结果已保存到: {filepath}")
        return filepath

def main():
    """主函数"""
    print("=" * 60)
    print("香港外国投资追踪器")
    print("=" * 60)
    
    tracker = HKForeignInvestmentTracker()
    
    # 进行分析
    results = tracker.analyze_foreign_investment_indicators()
    
    # 显示结果
    print(f"\n📅 分析时间: {results['分析时间']}")
    
    print(f"\n📊 数据源获取结果:")
    for source, info in results["数据源"].items():
        status_icon = "✅" if info["状态"] == "成功" else "❌"
        print(f"   {status_icon} {source}: {info['状态']}")
        if info["状态"] == "成功":
            print(f"      记录数: {info['记录数']}")
            if "说明" in info:
                print(f"      说明: {info['说明']}")
    
    print(f"\n📈 分析结果:")
    for analysis, data in results["分析结果"].items():
        print(f"   🔍 {analysis}:")
        for key, value in data.items():
            print(f"      {key}: {value}")
    
    print(f"\n💡 建议:")
    for i, suggestion in enumerate(results["建议"], 1):
        print(f"   {i}. {suggestion}")
    
    # 保存结果
    filepath = tracker.save_analysis_results(results)
    print(f"\n💾 详细分析结果已保存到: {filepath}")
    
    print(f"\n⚠️  关于真正的外资数据:")
    print("   目前获取的是间接指标，真正的外资流入数据需要:")
    print("   • 香港金管局的国际收支统计")
    print("   • 港交所的投资者分类数据")
    print("   • 专业金融数据提供商（Wind、Bloomberg等）")
    print("   • 机构投资者的持仓披露报告")
    
    print(f"\n✅ 分析完成！")

if __name__ == "__main__":
    main()
