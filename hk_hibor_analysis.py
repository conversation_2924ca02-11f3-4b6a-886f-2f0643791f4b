#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港HIBOR利率数据分析
分析不同期限的HIBOR利率趋势和特征
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import numpy as np
import os

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HKHiborAnalyzer:
    def __init__(self, data_file="data_files/hk_hibor_rates_full.csv"):
        """初始化分析器"""
        self.data_file = data_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载HIBOR数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data['报告日'] = pd.to_datetime(self.data['报告日'])
            self.data['利率'] = pd.to_numeric(self.data['利率'], errors='coerce')
            
            print(f"✅ 成功加载数据: {len(self.data)} 条记录")
            print(f"📅 数据时间范围: {self.data['报告日'].min()} 到 {self.data['报告日'].max()}")
            print(f"📊 包含期限: {', '.join(self.data['期限'].unique())}")
            
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return False
        
        return True
    
    def get_latest_rates(self):
        """获取最新利率"""
        if self.data is None:
            return None
        
        latest_data = self.data.groupby('期限').last().reset_index()
        latest_data = latest_data.sort_values('利率')
        
        print("\n📊 最新HIBOR利率 (按利率从低到高排序):")
        print("-" * 50)
        for _, row in latest_data.iterrows():
            print(f"{row['期限']:>6}: {row['利率']:>8.4f}% ({row['报告日'].strftime('%Y-%m-%d')})")
        
        return latest_data
    
    def analyze_rate_statistics(self):
        """分析利率统计特征"""
        if self.data is None:
            return None
        
        print("\n📈 HIBOR利率统计分析:")
        print("=" * 60)
        
        stats_data = []
        for term in self.data['期限'].unique():
            term_data = self.data[self.data['期限'] == term]['利率']
            
            stats = {
                '期限': term,
                '平均值': term_data.mean(),
                '中位数': term_data.median(),
                '标准差': term_data.std(),
                '最小值': term_data.min(),
                '最大值': term_data.max(),
                '数据点数': len(term_data)
            }
            stats_data.append(stats)
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('平均值')
        
        print(stats_df.to_string(index=False, float_format='%.4f'))
        
        return stats_df
    
    def plot_hibor_trends_dual(self):
        """绘制双时间段HIBOR利率变化图：长期趋势(2000年至今)和短期趋势(今年至今)"""
        if self.data is None:
            return

        # 创建双子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

        # 定义颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

        # 图1：从2000年开始的长期趋势
        cutoff_date_long = datetime(2000, 1, 1)
        long_term_data = self.data[self.data['报告日'] >= cutoff_date_long].copy()

        if not long_term_data.empty:
            latest_points_long = []
            for i, term in enumerate(sorted(long_term_data['期限'].unique())):
                term_data = long_term_data[long_term_data['期限'] == term].copy()
                term_data = term_data.sort_values('报告日')

                color = colors[i % len(colors)]

                # 绘制长期趋势线
                ax1.plot(term_data['报告日'], term_data['利率'],
                        label=f'{term}', linewidth=2, alpha=0.8, color=color)

                # 获取最新点
                if not term_data.empty:
                    latest = term_data.iloc[-1]
                    latest_points_long.append({
                        '期限': term,
                        '日期': latest['报告日'],
                        '利率': latest['利率'],
                        '颜色': color
                    })

            # 标识最新利率点
            for point in latest_points_long:
                ax1.scatter(point['日期'], point['利率'],
                           color=point['颜色'], s=100, zorder=5, edgecolors='white', linewidth=2)

                # 添加最新利率标签
                ax1.annotate(f'{point["利率"]:.3f}%',
                            xy=(point['日期'], point['利率']),
                            xytext=(10, 10), textcoords='offset points',
                            bbox=dict(boxstyle='round,pad=0.3', facecolor=point['颜色'], alpha=0.7, edgecolor='white'),
                            fontsize=9, fontweight='bold', color='white',
                            ha='left', va='bottom')

            ax1.set_title('香港HIBOR利率长期趋势 (2000年至今)', fontsize=14, fontweight='bold', pad=15)
            ax1.set_ylabel('利率 (%)', fontsize=12)
            ax1.legend(loc='upper left', frameon=True, fancybox=True, shadow=True, fontsize=10)
            ax1.grid(True, alpha=0.3, linestyle='--')
            ax1.tick_params(axis='x', rotation=45)

            # 设置y轴范围
            y_min_long = long_term_data['利率'].min() * 0.9
            y_max_long = long_term_data['利率'].max() * 1.1
            ax1.set_ylim(y_min_long, y_max_long)

        # 图2：从今年开始的短期趋势
        current_year = datetime.now().year
        cutoff_date_short = datetime(current_year, 1, 1)
        short_term_data = self.data[self.data['报告日'] >= cutoff_date_short].copy()

        if not short_term_data.empty:
            latest_points_short = []
            for i, term in enumerate(sorted(short_term_data['期限'].unique())):
                term_data = short_term_data[short_term_data['期限'] == term].copy()
                term_data = term_data.sort_values('报告日')

                color = colors[i % len(colors)]

                # 绘制短期趋势线（更粗的线条和更大的标记点）
                ax2.plot(term_data['报告日'], term_data['利率'],
                        label=f'{term}', linewidth=3, alpha=0.9, color=color, marker='o', markersize=4)

                # 获取最新点
                if not term_data.empty:
                    latest = term_data.iloc[-1]
                    latest_points_short.append({
                        '期限': term,
                        '日期': latest['报告日'],
                        '利率': latest['利率'],
                        '颜色': color
                    })

            # 标识最新利率点（更大的点）
            for point in latest_points_short:
                ax2.scatter(point['日期'], point['利率'],
                           color=point['颜色'], s=150, zorder=5, edgecolors='white', linewidth=3)

                # 添加最新利率标签（更大的字体）
                ax2.annotate(f'{point["利率"]:.3f}%',
                            xy=(point['日期'], point['利率']),
                            xytext=(12, 12), textcoords='offset points',
                            bbox=dict(boxstyle='round,pad=0.4', facecolor=point['颜色'], alpha=0.8, edgecolor='white'),
                            fontsize=11, fontweight='bold', color='white',
                            ha='left', va='bottom')

            ax2.set_title(f'香港HIBOR利率短期趋势 ({current_year}年至今)', fontsize=14, fontweight='bold', pad=15)
            ax2.set_xlabel('日期', fontsize=12)
            ax2.set_ylabel('利率 (%)', fontsize=12)
            ax2.legend(loc='upper left', frameon=True, fancybox=True, shadow=True, fontsize=10)
            ax2.grid(True, alpha=0.3, linestyle='--')
            ax2.tick_params(axis='x', rotation=45)

            # 设置y轴范围
            y_min_short = short_term_data['利率'].min() * 0.9
            y_max_short = short_term_data['利率'].max() * 1.1
            ax2.set_ylim(y_min_short, y_max_short)
        else:
            ax2.text(0.5, 0.5, f'暂无{current_year}年数据', ha='center', va='center',
                    transform=ax2.transAxes, fontsize=14, color='gray')
            ax2.set_title(f'香港HIBOR利率短期趋势 ({current_year}年至今)', fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存图表
        output_file = f"hk_hibor_trends_dual_2000_and_{current_year}.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"\n📊 HIBOR双时间段趋势图已保存: {output_file}")

        plt.show()
    
    def analyze_yield_curve(self):
        """分析收益率曲线"""
        if self.data is None:
            return
        
        # 获取最新数据
        latest_data = self.data.groupby('期限').last().reset_index()
        
        # 定义期限排序顺序
        term_order = ['隔夜', '1周', '2周', '1月', '2月', '3月', '6月', '12月']
        
        # 筛选并排序数据
        curve_data = []
        for term in term_order:
            if term in latest_data['期限'].values:
                rate = latest_data[latest_data['期限'] == term]['利率'].iloc[0]
                curve_data.append({'期限': term, '利率': rate})
        
        if len(curve_data) < 2:
            print("❌ 数据不足，无法绘制收益率曲线")
            return
        
        curve_df = pd.DataFrame(curve_data)
        
        print("\n📈 香港HIBOR收益率曲线:")
        print("-" * 40)
        for _, row in curve_df.iterrows():
            print(f"{row['期限']:>6}: {row['利率']:>8.4f}%")
        
        # 绘制收益率曲线
        plt.figure(figsize=(12, 6))
        
        plt.plot(range(len(curve_df)), curve_df['利率'], 
                marker='o', linewidth=2, markersize=8, color='blue')
        
        plt.title('香港HIBOR收益率曲线', fontsize=16, fontweight='bold')
        plt.xlabel('期限', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.xticks(range(len(curve_df)), curve_df['期限'], rotation=45)
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, row in curve_df.iterrows():
            plt.annotate(f'{row["利率"]:.3f}%', 
                        (i, row['利率']), 
                        textcoords="offset points", 
                        xytext=(0,10), 
                        ha='center', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = "hk_hibor_yield_curve.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n📊 收益率曲线图已保存: {output_file}")
        
        plt.show()
        
        return curve_df

def main():
    """主函数"""
    print("=" * 60)
    print("香港HIBOR利率变化图")
    print("=" * 60)

    # 创建分析器
    analyzer = HKHiborAnalyzer()

    if analyzer.data is None:
        print("❌ 无法加载数据，请先运行 hk_hibor_collector.py")
        return

    # 显示最新利率
    analyzer.get_latest_rates()

    # 绘制利率变化趋势图（从2000年开始）
    analyzer.plot_hibor_trends(start_year=2000)

    print("\n✅ 图表生成完成！")

if __name__ == "__main__":
    main()
