#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港HIBOR利率数据分析
分析不同期限的HIBOR利率趋势和特征
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import numpy as np
import os

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HKHiborAnalyzer:
    def __init__(self, data_file="data_files/hk_hibor_rates_full.csv"):
        """初始化分析器"""
        self.data_file = data_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载HIBOR数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data['报告日'] = pd.to_datetime(self.data['报告日'])
            self.data['利率'] = pd.to_numeric(self.data['利率'], errors='coerce')
            
            print(f"✅ 成功加载数据: {len(self.data)} 条记录")
            print(f"📅 数据时间范围: {self.data['报告日'].min()} 到 {self.data['报告日'].max()}")
            print(f"📊 包含期限: {', '.join(self.data['期限'].unique())}")
            
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return False
        
        return True
    
    def get_latest_rates(self):
        """获取最新利率"""
        if self.data is None:
            return None
        
        latest_data = self.data.groupby('期限').last().reset_index()
        latest_data = latest_data.sort_values('利率')
        
        print("\n📊 最新HIBOR利率 (按利率从低到高排序):")
        print("-" * 50)
        for _, row in latest_data.iterrows():
            print(f"{row['期限']:>6}: {row['利率']:>8.4f}% ({row['报告日'].strftime('%Y-%m-%d')})")
        
        return latest_data
    
    def analyze_rate_statistics(self):
        """分析利率统计特征"""
        if self.data is None:
            return None
        
        print("\n📈 HIBOR利率统计分析:")
        print("=" * 60)
        
        stats_data = []
        for term in self.data['期限'].unique():
            term_data = self.data[self.data['期限'] == term]['利率']
            
            stats = {
                '期限': term,
                '平均值': term_data.mean(),
                '中位数': term_data.median(),
                '标准差': term_data.std(),
                '最小值': term_data.min(),
                '最大值': term_data.max(),
                '数据点数': len(term_data)
            }
            stats_data.append(stats)
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('平均值')
        
        print(stats_df.to_string(index=False, float_format='%.4f'))
        
        return stats_df
    
    def plot_hibor_trends(self, recent_years=5):
        """绘制HIBOR利率趋势图"""
        if self.data is None:
            return
        
        # 筛选最近几年的数据
        cutoff_date = datetime.now() - timedelta(days=recent_years*365)
        recent_data = self.data[self.data['报告日'] >= cutoff_date].copy()
        
        if recent_data.empty:
            print("❌ 没有足够的近期数据用于绘图")
            return
        
        # 创建图表
        plt.figure(figsize=(15, 10))
        
        # 主图：利率趋势
        plt.subplot(2, 2, 1)
        for term in recent_data['期限'].unique():
            term_data = recent_data[recent_data['期限'] == term]
            plt.plot(term_data['报告日'], term_data['利率'], 
                    label=f'{term}', linewidth=1.5, alpha=0.8)
        
        plt.title(f'香港HIBOR利率趋势 (最近{recent_years}年)', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        # 子图2：最新利率对比
        plt.subplot(2, 2, 2)
        latest_data = recent_data.groupby('期限').last().reset_index()
        latest_data = latest_data.sort_values('利率')
        
        bars = plt.bar(range(len(latest_data)), latest_data['利率'], 
                      color=plt.cm.viridis(np.linspace(0, 1, len(latest_data))))
        plt.title('最新HIBOR利率对比', fontsize=14, fontweight='bold')
        plt.xlabel('期限', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.xticks(range(len(latest_data)), latest_data['期限'], rotation=45)
        
        # 在柱状图上添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}%', ha='center', va='bottom', fontsize=10)
        
        # 子图3：利率分布箱线图
        plt.subplot(2, 2, 3)
        terms_for_box = []
        rates_for_box = []
        
        for term in recent_data['期限'].unique():
            term_rates = recent_data[recent_data['期限'] == term]['利率'].values
            terms_for_box.extend([term] * len(term_rates))
            rates_for_box.extend(term_rates)
        
        box_data = pd.DataFrame({'期限': terms_for_box, '利率': rates_for_box})
        sns.boxplot(data=box_data, x='期限', y='利率')
        plt.title(f'HIBOR利率分布 (最近{recent_years}年)', fontsize=14, fontweight='bold')
        plt.xlabel('期限', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.xticks(rotation=45)
        
        # 子图4：利率波动性分析
        plt.subplot(2, 2, 4)
        volatility_data = []
        
        for term in recent_data['期限'].unique():
            term_data = recent_data[recent_data['期限'] == term].copy()
            term_data = term_data.sort_values('报告日')
            
            # 计算30天滚动标准差作为波动性指标
            if len(term_data) >= 30:
                term_data['volatility'] = term_data['利率'].rolling(window=30).std()
                avg_volatility = term_data['volatility'].mean()
                volatility_data.append({'期限': term, '平均波动性': avg_volatility})
        
        if volatility_data:
            vol_df = pd.DataFrame(volatility_data)
            vol_df = vol_df.sort_values('平均波动性')
            
            bars = plt.bar(range(len(vol_df)), vol_df['平均波动性'],
                          color=plt.cm.plasma(np.linspace(0, 1, len(vol_df))))
            plt.title('HIBOR利率波动性对比', fontsize=14, fontweight='bold')
            plt.xlabel('期限', fontsize=12)
            plt.ylabel('波动性 (30天滚动标准差)', fontsize=12)
            plt.xticks(range(len(vol_df)), vol_df['期限'], rotation=45)
            
            # 添加数值标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = f"hk_hibor_analysis_{recent_years}years.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n📊 图表已保存: {output_file}")
        
        plt.show()
    
    def analyze_yield_curve(self):
        """分析收益率曲线"""
        if self.data is None:
            return
        
        # 获取最新数据
        latest_data = self.data.groupby('期限').last().reset_index()
        
        # 定义期限排序顺序
        term_order = ['隔夜', '1周', '2周', '1月', '2月', '3月', '6月', '12月']
        
        # 筛选并排序数据
        curve_data = []
        for term in term_order:
            if term in latest_data['期限'].values:
                rate = latest_data[latest_data['期限'] == term]['利率'].iloc[0]
                curve_data.append({'期限': term, '利率': rate})
        
        if len(curve_data) < 2:
            print("❌ 数据不足，无法绘制收益率曲线")
            return
        
        curve_df = pd.DataFrame(curve_data)
        
        print("\n📈 香港HIBOR收益率曲线:")
        print("-" * 40)
        for _, row in curve_df.iterrows():
            print(f"{row['期限']:>6}: {row['利率']:>8.4f}%")
        
        # 绘制收益率曲线
        plt.figure(figsize=(12, 6))
        
        plt.plot(range(len(curve_df)), curve_df['利率'], 
                marker='o', linewidth=2, markersize=8, color='blue')
        
        plt.title('香港HIBOR收益率曲线', fontsize=16, fontweight='bold')
        plt.xlabel('期限', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.xticks(range(len(curve_df)), curve_df['期限'], rotation=45)
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, row in curve_df.iterrows():
            plt.annotate(f'{row["利率"]:.3f}%', 
                        (i, row['利率']), 
                        textcoords="offset points", 
                        xytext=(0,10), 
                        ha='center', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = "hk_hibor_yield_curve.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n📊 收益率曲线图已保存: {output_file}")
        
        plt.show()
        
        return curve_df

def main():
    """主函数"""
    print("=" * 60)
    print("香港HIBOR利率数据分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = HKHiborAnalyzer()
    
    if analyzer.data is None:
        print("❌ 无法加载数据，请先运行 hk_hibor_collector.py")
        return
    
    # 1. 显示最新利率
    analyzer.get_latest_rates()
    
    # 2. 统计分析
    analyzer.analyze_rate_statistics()
    
    # 3. 绘制趋势图
    analyzer.plot_hibor_trends(recent_years=3)
    
    # 4. 分析收益率曲线
    analyzer.analyze_yield_curve()
    
    print("\n✅ 分析完成！")

if __name__ == "__main__":
    main()
