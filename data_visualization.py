#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化脚本
可视化收集到的香港HIBOR利率和外资流入相关数据
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import json
from datetime import datetime
import os

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class DataVisualizer:
    """数据可视化器"""
    
    def __init__(self):
        """初始化"""
        self.data_dir = "data_files"
        self.output_dir = "visualizations"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def load_hibor_data(self):
        """加载HIBOR数据"""
        try:
            hibor_file = os.path.join(self.data_dir, "hk_hibor_rates_latest.csv")
            if os.path.exists(hibor_file):
                return pd.read_csv(hibor_file)
            return None
        except Exception as e:
            print(f"加载HIBOR数据失败: {str(e)}")
            return None
    
    def load_capital_flow_data(self):
        """加载资金流向数据"""
        try:
            # 查找最新的资金流向数据文件
            files = [f for f in os.listdir(self.data_dir) if f.startswith("hk_foreign_capital_summary_")]
            if files:
                latest_file = sorted(files)[-1]
                file_path = os.path.join(self.data_dir, latest_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"加载资金流向数据失败: {str(e)}")
            return None
    
    def load_foreign_investment_data(self):
        """加载外国投资分析数据"""
        try:
            # 查找最新的外国投资分析文件
            files = [f for f in os.listdir(self.data_dir) if f.startswith("hk_foreign_investment_analysis_")]
            if files:
                latest_file = sorted(files)[-1]
                file_path = os.path.join(self.data_dir, latest_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"加载外国投资数据失败: {str(e)}")
            return None
    
    def create_comprehensive_dashboard(self):
        """创建综合数据仪表板"""
        # 创建大图表
        fig = plt.figure(figsize=(20, 12))
        
        # 加载所有数据
        hibor_data = self.load_hibor_data()
        capital_flow_data = self.load_capital_flow_data()
        foreign_investment_data = self.load_foreign_investment_data()
        
        # 1. HIBOR利率图表 (左上)
        ax1 = plt.subplot(2, 3, 1)
        if hibor_data is not None:
            terms = hibor_data['期限'].tolist()
            rates = hibor_data['利率'].tolist()
            colors = plt.cm.viridis(np.linspace(0, 1, len(terms)))
            
            bars = ax1.bar(terms, rates, color=colors, alpha=0.8)
            ax1.set_title('香港HIBOR利率 (最新)', fontsize=14, fontweight='bold')
            ax1.set_ylabel('利率 (%)', fontsize=12)
            ax1.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, rate in zip(bars, rates):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{rate:.3f}%', ha='center', va='bottom', fontsize=10)
        else:
            ax1.text(0.5, 0.5, '无HIBOR数据', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('香港HIBOR利率', fontsize=14)
        
        # 2. 资金流向对比图 (右上)
        ax2 = plt.subplot(2, 3, 2)
        if capital_flow_data and '资金流向对比' in capital_flow_data:
            comparison = capital_flow_data['资金流向对比']
            categories = ['南向资金\n(流入香港)', '北向资金\n(流入内地)']
            values = [comparison['南向资金净流入'], comparison['北向资金净流入']]
            colors = ['green' if v > 0 else 'red' for v in values]
            
            bars = ax2.bar(categories, values, color=colors, alpha=0.7)
            ax2.set_title('资金流向对比', fontsize=14, fontweight='bold')
            ax2.set_ylabel('净流入 (亿元)', fontsize=12)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                y_pos = height + (1 if height >= 0 else -2)
                ax2.text(bar.get_x() + bar.get_width()/2., y_pos,
                        f'{value:.1f}', ha='center', va='bottom' if height >= 0 else 'top', 
                        fontsize=11, fontweight='bold')
        else:
            ax2.text(0.5, 0.5, '无资金流向数据', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('资金流向对比', fontsize=14)
        
        # 3. 港股通详情饼图 (左中)
        ax3 = plt.subplot(2, 3, 3)
        if capital_flow_data and '南向资金' in capital_flow_data:
            south_data = capital_flow_data['南向资金']
            if '港股通详情' in south_data:
                details = south_data['港股通详情']
                labels = [d['板块'] for d in details]
                values = [abs(d['净买额']) for d in details]  # 使用绝对值
                colors = ['lightgreen' if d['净买额'] > 0 else 'lightcoral' for d in details]
                
                wedges, texts, autotexts = ax3.pie(values, labels=labels, colors=colors, 
                                                  autopct='%1.1f%%', startangle=90)
                ax3.set_title('港股通资金分布', fontsize=14, fontweight='bold')
                
                # 添加图例说明流入/流出
                legend_labels = [f"{d['板块']}: {'流入' if d['净买额'] > 0 else '流出'}" for d in details]
                ax3.legend(wedges, legend_labels, loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        else:
            ax3.text(0.5, 0.5, '无港股通数据', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('港股通资金分布', fontsize=14)
        
        # 4. ADR交易量图表 (左下)
        ax4 = plt.subplot(2, 3, 4)
        if foreign_investment_data and 'ADR分析' in foreign_investment_data['分析结果']:
            adr_analysis = foreign_investment_data['分析结果']['ADR分析']
            
            # 创建ADR指标图
            metrics = ['总交易量\n(万股)', '平均价格\n(美元)', '活跃ADR\n(只数)']
            values = [
                adr_analysis['总交易量'] / 10000,  # 转换为万股
                adr_analysis['平均价格'],
                adr_analysis['活跃ADR数量']
            ]
            
            # 标准化数值用于显示
            normalized_values = [v/max(values) * 100 for v in values]
            colors = ['skyblue', 'lightgreen', 'orange']
            
            bars = ax4.bar(metrics, normalized_values, color=colors, alpha=0.7)
            ax4.set_title('香港ADR交易指标', fontsize=14, fontweight='bold')
            ax4.set_ylabel('标准化数值', fontsize=12)
            
            # 添加实际数值标签
            for bar, actual_value in zip(bars, values):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 2,
                        f'{actual_value:.0f}', ha='center', va='bottom', fontsize=10)
        else:
            ax4.text(0.5, 0.5, '无ADR数据', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('香港ADR交易指标', fontsize=14)
        
        # 5. ETF交易分析 (中下)
        ax5 = plt.subplot(2, 3, 5)
        if foreign_investment_data and 'ETF分析' in foreign_investment_data['分析结果']:
            etf_analysis = foreign_investment_data['分析结果']['ETF分析']
            
            # 创建ETF指标
            etf_volume = etf_analysis['总交易量'] / 10000  # 转换为万股
            etf_count = etf_analysis['活跃ETF数量']
            
            categories = ['ETF交易量\n(万股)', 'ETF数量\n(只)']
            values = [etf_volume, etf_count]
            colors = ['purple', 'gold']
            
            bars = ax5.bar(categories, values, color=colors, alpha=0.7)
            ax5.set_title('香港相关ETF指标', fontsize=14, fontweight='bold')
            ax5.set_ylabel('数值', fontsize=12)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax5.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.02,
                        f'{value:.0f}', ha='center', va='bottom', fontsize=11, fontweight='bold')
        else:
            ax5.text(0.5, 0.5, '无ETF数据', ha='center', va='center', transform=ax5.transAxes)
            ax5.set_title('香港相关ETF指标', fontsize=14)
        
        # 6. 市场表现总结 (右下)
        ax6 = plt.subplot(2, 3, 6)
        if capital_flow_data and '南向资金' in capital_flow_data and '市场表现' in capital_flow_data['南向资金']:
            market = capital_flow_data['南向资金']['市场表现']
            
            # 创建市场表现指标
            hsi_change = market['恒生指数涨跌幅']
            up_down_ratio = market['上涨下跌比']
            
            # 绘制恒生指数涨跌
            ax6_twin = ax6.twinx()
            
            # 恒生指数涨跌幅
            color = 'green' if hsi_change > 0 else 'red'
            ax6.bar(['恒生指数涨跌幅'], [hsi_change], color=color, alpha=0.7, width=0.4)
            ax6.set_ylabel('涨跌幅 (%)', fontsize=12, color=color)
            ax6.tick_params(axis='y', labelcolor=color)
            
            # 上涨下跌比
            ax6_twin.bar(['上涨/下跌比'], [up_down_ratio], color='blue', alpha=0.7, width=0.4)
            ax6_twin.set_ylabel('比率', fontsize=12, color='blue')
            ax6_twin.tick_params(axis='y', labelcolor='blue')
            
            ax6.set_title('市场表现指标', fontsize=14, fontweight='bold')
            
            # 添加数值标签
            ax6.text(0, hsi_change + (0.1 if hsi_change > 0 else -0.1),
                    f'{hsi_change:+.2f}%', ha='center', va='bottom' if hsi_change > 0 else 'top',
                    fontsize=11, fontweight='bold')
            ax6_twin.text(0, up_down_ratio + 0.05,
                         f'{up_down_ratio:.2f}', ha='center', va='bottom',
                         fontsize=11, fontweight='bold')
        else:
            ax6.text(0.5, 0.5, '无市场表现数据', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('市场表现指标', fontsize=14)
        
        # 调整布局
        plt.tight_layout(pad=3.0)
        
        # 添加总标题
        fig.suptitle('香港金融市场数据综合仪表板', fontsize=18, fontweight='bold', y=0.98)
        
        # 添加数据更新时间
        update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        fig.text(0.99, 0.01, f'数据更新时间: {update_time}', ha='right', va='bottom', fontsize=10, alpha=0.7)
        
        # 保存图表
        output_file = os.path.join(self.output_dir, f"hk_financial_dashboard_{datetime.now().strftime('%Y%m%d_%H%M')}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"📊 综合仪表板已保存: {output_file}")
        
        plt.show()
    
    def create_summary_report(self):
        """创建数据摘要报告"""
        print("=" * 60)
        print("香港金融市场数据可视化摘要")
        print("=" * 60)
        
        # 加载数据
        hibor_data = self.load_hibor_data()
        capital_flow_data = self.load_capital_flow_data()
        foreign_investment_data = self.load_foreign_investment_data()
        
        print(f"\n📊 数据可视化内容:")
        
        if hibor_data is not None:
            print(f"✅ HIBOR利率数据: {len(hibor_data)} 个期限")
            print(f"   最低利率: {hibor_data['利率'].min():.3f}% ({hibor_data.loc[hibor_data['利率'].idxmin(), '期限']})")
            print(f"   最高利率: {hibor_data['利率'].max():.3f}% ({hibor_data.loc[hibor_data['利率'].idxmax(), '期限']})")
        
        if capital_flow_data:
            print(f"✅ 资金流向数据: {capital_flow_data['数据更新时间']}")
            if '资金流向对比' in capital_flow_data:
                comparison = capital_flow_data['资金流向对比']
                print(f"   南向资金净流入: {comparison['南向资金净流入']:+.1f} 亿元")
                print(f"   北向资金净流入: {comparison['北向资金净流入']:+.1f} 亿元")
        
        if foreign_investment_data:
            print(f"✅ 外国投资指标: {foreign_investment_data['分析时间']}")
            if 'ADR分析' in foreign_investment_data['分析结果']:
                adr = foreign_investment_data['分析结果']['ADR分析']
                print(f"   ADR总交易量: {adr['总交易量']:,} 股")
                print(f"   活跃ADR数量: {adr['活跃ADR数量']} 只")
            if 'ETF分析' in foreign_investment_data['分析结果']:
                etf = foreign_investment_data['分析结果']['ETF分析']
                print(f"   ETF总交易量: {etf['总交易量']:,} 股")
        
        print(f"\n📈 可视化图表包含:")
        print("   1. 香港HIBOR利率分布")
        print("   2. 南向/北向资金流向对比")
        print("   3. 港股通资金分布")
        print("   4. 香港ADR交易指标")
        print("   5. 香港相关ETF指标")
        print("   6. 市场表现总结")

def main():
    """主函数"""
    visualizer = DataVisualizer()
    
    # 创建摘要报告
    visualizer.create_summary_report()
    
    # 创建综合仪表板
    print(f"\n🎨 正在生成可视化图表...")
    visualizer.create_comprehensive_dashboard()
    
    print(f"\n✅ 数据可视化完成！")

if __name__ == "__main__":
    main()
