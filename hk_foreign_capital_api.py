#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港外资流入API
提供便捷的香港外资流入数据获取和分析功能
"""

import akshare as ak
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, List
import os

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HKForeignCapitalAPI:
    """香港外资流入API类"""
    
    def __init__(self):
        """初始化API"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
    
    def get_southbound_capital_flow(self) -> Optional[pd.DataFrame]:
        """
        获取南向资金流向数据（内地资金流入香港）
        
        Returns:
            pandas.DataFrame: 南向资金流向数据
        """
        try:
            logger.info("正在获取南向资金流向数据...")
            
            # 获取沪深港通资金流向汇总数据
            data = ak.stock_hsgt_fund_flow_summary_em()
            
            if data is None or data.empty:
                logger.warning("未获取到南向资金流向数据")
                return None
            
            # 筛选南向资金（港股通）
            southbound_data = data[data['资金方向'] == '南向'].copy()
            
            if southbound_data.empty:
                logger.warning("未找到南向资金数据")
                return None
            
            # 数据处理
            southbound_data['交易日'] = pd.to_datetime(southbound_data['交易日'])
            southbound_data['成交净买额'] = pd.to_numeric(southbound_data['成交净买额'], errors='coerce')
            southbound_data['资金净流入'] = pd.to_numeric(southbound_data['资金净流入'], errors='coerce')
            
            logger.info(f"成功获取南向资金流向数据: {len(southbound_data)} 条记录")
            return southbound_data
            
        except Exception as e:
            logger.error(f"获取南向资金流向数据时出错: {str(e)}")
            return None
    
    def get_hsgt_historical_data(self, days: int = 30) -> Optional[pd.DataFrame]:
        """
        获取沪深港通历史资金流向数据
        
        Args:
            days: 获取最近多少天的数据
            
        Returns:
            pandas.DataFrame: 历史资金流向数据
        """
        try:
            logger.info(f"正在获取最近{days}天的沪深港通历史数据...")
            
            # 获取沪深港通历史数据
            hist_data = ak.stock_hsgt_hist_em()
            
            if hist_data is None or hist_data.empty:
                logger.warning("未获取到历史数据")
                return None
            
            # 数据处理
            hist_data['交易日期'] = pd.to_datetime(hist_data['交易日期'])
            
            # 筛选最近的数据
            if days > 0:
                cutoff_date = datetime.now() - timedelta(days=days)
                hist_data = hist_data[hist_data['交易日期'] >= cutoff_date]
            
            # 数值列转换
            numeric_columns = ['沪股通净流入', '深股通净流入', '港股通净流入', '北向资金', '南向资金']
            for col in numeric_columns:
                if col in hist_data.columns:
                    hist_data[col] = pd.to_numeric(hist_data[col], errors='coerce')
            
            logger.info(f"成功获取历史数据: {len(hist_data)} 条记录")
            return hist_data
            
        except Exception as e:
            logger.error(f"获取历史数据时出错: {str(e)}")
            return None
    
    def get_latest_capital_flow(self) -> Optional[Dict]:
        """
        获取最新的资金流向数据
        
        Returns:
            dict: 最新资金流向信息
        """
        southbound_data = self.get_southbound_capital_flow()
        
        if southbound_data is None or southbound_data.empty:
            return None
        
        # 获取最新交易日数据
        latest_date = southbound_data['交易日'].max()
        latest_data = southbound_data[southbound_data['交易日'] == latest_date]
        
        # 汇总南向资金
        total_net_inflow = latest_data['成交净买额'].sum()
        
        result = {
            "交易日": latest_date.strftime('%Y-%m-%d'),
            "南向资金净流入": float(total_net_inflow),
            "更新时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "详细数据": []
        }
        
        for _, row in latest_data.iterrows():
            result["详细数据"].append({
                "板块": row['板块'],
                "成交净买额": float(row['成交净买额']),
                "相关指数": row['相关指数'],
                "指数涨跌幅": float(row['指数涨跌幅']) if pd.notna(row['指数涨跌幅']) else 0.0
            })
        
        return result
    
    def analyze_capital_flow_trends(self, days: int = 30) -> Optional[Dict]:
        """
        分析资金流向趋势
        
        Args:
            days: 分析最近多少天的数据
            
        Returns:
            dict: 趋势分析结果
        """
        hist_data = self.get_hsgt_historical_data(days)
        
        if hist_data is None or hist_data.empty:
            return None
        
        # 计算趋势指标
        if '南向资金' in hist_data.columns:
            southbound_flows = hist_data['南向资金'].dropna()
            
            if len(southbound_flows) > 0:
                total_inflow = southbound_flows.sum()
                avg_daily_inflow = southbound_flows.mean()
                positive_days = (southbound_flows > 0).sum()
                negative_days = (southbound_flows < 0).sum()
                
                # 计算趋势（最近7天vs前7天）
                if len(southbound_flows) >= 14:
                    recent_avg = southbound_flows.tail(7).mean()
                    previous_avg = southbound_flows.iloc[-14:-7].mean()
                    trend_change = ((recent_avg - previous_avg) / abs(previous_avg)) * 100 if previous_avg != 0 else 0
                else:
                    trend_change = 0
                
                return {
                    "分析期间": f"最近{days}天",
                    "总净流入": float(total_inflow),
                    "日均净流入": float(avg_daily_inflow),
                    "净流入天数": int(positive_days),
                    "净流出天数": int(negative_days),
                    "趋势变化": f"{trend_change:.2f}%",
                    "数据点数": len(southbound_flows)
                }
        
        return None
    
    def plot_capital_flow_trends(self, days: int = 60):
        """
        绘制资金流向趋势图
        
        Args:
            days: 绘制最近多少天的数据
        """
        hist_data = self.get_hsgt_historical_data(days)
        
        if hist_data is None or hist_data.empty:
            logger.warning("没有数据可供绘图")
            return
        
        # 创建图表
        plt.figure(figsize=(14, 8))
        
        # 绘制南向资金流向
        if '南向资金' in hist_data.columns:
            southbound_data = hist_data[['交易日期', '南向资金']].dropna()
            
            if not southbound_data.empty:
                # 绘制柱状图
                colors = ['green' if x > 0 else 'red' for x in southbound_data['南向资金']]
                plt.bar(southbound_data['交易日期'], southbound_data['南向资金'], 
                       color=colors, alpha=0.7, width=0.8)
                
                # 添加零线
                plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                
                # 添加移动平均线
                if len(southbound_data) >= 5:
                    southbound_data['MA5'] = southbound_data['南向资金'].rolling(window=5).mean()
                    plt.plot(southbound_data['交易日期'], southbound_data['MA5'], 
                            color='blue', linewidth=2, label='5日移动平均')
                
                # 标识最新数据点
                if not southbound_data.empty:
                    latest = southbound_data.iloc[-1]
                    plt.scatter(latest['交易日期'], latest['南向资金'], 
                              color='orange', s=100, zorder=5, edgecolors='white', linewidth=2)
                    
                    # 添加最新数值标签
                    plt.annotate(f'{latest["南向资金"]:.1f}亿', 
                                xy=(latest['交易日期'], latest['南向资金']),
                                xytext=(10, 10), textcoords='offset points',
                                bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                                fontsize=10, fontweight='bold',
                                ha='left', va='bottom')
        
        plt.title(f'南向资金流入香港趋势 (最近{days}天)', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('交易日期', fontsize=12)
        plt.ylabel('资金净流入 (亿元)', fontsize=12)
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.xticks(rotation=45)
        
        if '南向资金' in hist_data.columns and len(hist_data[['交易日期', '南向资金']].dropna()) >= 5:
            plt.legend()
        
        plt.tight_layout()
        
        # 保存图表
        output_file = f"hk_foreign_capital_trends_{days}days.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        logger.info(f"资金流向趋势图已保存: {output_file}")
        
        plt.show()
    
    def save_latest_data(self):
        """保存最新数据到文件"""
        # 获取南向资金数据
        southbound_data = self.get_southbound_capital_flow()
        if southbound_data is not None:
            filename = f"hk_southbound_capital_{datetime.now().strftime('%Y%m%d')}.csv"
            filepath = os.path.join(self.data_dir, filename)
            southbound_data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"南向资金数据已保存: {filepath}")
        
        # 获取历史数据
        hist_data = self.get_hsgt_historical_data(30)
        if hist_data is not None:
            filename = f"hk_hsgt_historical_{datetime.now().strftime('%Y%m%d')}.csv"
            filepath = os.path.join(self.data_dir, filename)
            hist_data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"沪深港通历史数据已保存: {filepath}")

def main():
    """演示API使用"""
    print("=" * 60)
    print("香港外资流入API演示")
    print("=" * 60)
    
    # 创建API实例
    api = HKForeignCapitalAPI()
    
    # 1. 获取最新资金流向
    print("\n📊 最新南向资金流向:")
    latest_flow = api.get_latest_capital_flow()
    if latest_flow:
        print(f"交易日: {latest_flow['交易日']}")
        print(f"南向资金净流入: {latest_flow['南向资金净流入']:.2f} 亿元")
        print("详细数据:")
        for detail in latest_flow['详细数据']:
            print(f"  {detail['板块']}: {detail['成交净买额']:.2f} 亿元")
    
    # 2. 分析趋势
    print("\n📈 资金流向趋势分析:")
    trend_analysis = api.analyze_capital_flow_trends(30)
    if trend_analysis:
        print(f"分析期间: {trend_analysis['分析期间']}")
        print(f"总净流入: {trend_analysis['总净流入']:.2f} 亿元")
        print(f"日均净流入: {trend_analysis['日均净流入']:.2f} 亿元")
        print(f"净流入天数: {trend_analysis['净流入天数']} 天")
        print(f"净流出天数: {trend_analysis['净流出天数']} 天")
        print(f"趋势变化: {trend_analysis['趋势变化']}")
    
    # 3. 绘制趋势图
    print("\n📉 绘制资金流向趋势图...")
    api.plot_capital_flow_trends(60)
    
    # 4. 保存数据
    print("\n💾 保存最新数据...")
    api.save_latest_data()
    
    print("\n✅ API演示完成！")

if __name__ == "__main__":
    main()
