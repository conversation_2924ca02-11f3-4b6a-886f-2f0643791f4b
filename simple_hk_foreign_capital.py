#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版香港外资流入数据获取器
专注于获取南向资金（内地资金流入香港）的核心数据
"""

import akshare as ak
import pandas as pd
import json
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleHKForeignCapital:
    """简化版香港外资流入API"""
    
    def __init__(self):
        """初始化"""
        pass
    
    def get_latest_southbound_flow(self):
        """
        获取最新的南向资金流向数据
        
        Returns:
            dict: 南向资金流向信息
        """
        try:
            logger.info("正在获取最新南向资金流向数据...")
            
            # 获取沪深港通资金流向汇总数据
            data = ak.stock_hsgt_fund_flow_summary_em()
            
            if data is None or data.empty:
                logger.warning("未获取到数据")
                return None
            
            # 筛选南向资金（港股通）
            southbound_data = data[data['资金方向'] == '南向'].copy()
            
            if southbound_data.empty:
                logger.warning("未找到南向资金数据")
                return None
            
            # 处理数据
            result = {
                "更新时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "交易日": None,
                "南向资金总净流入": 0.0,
                "港股通详情": [],
                "市场表现": {}
            }
            
            total_net_inflow = 0.0
            
            for _, row in southbound_data.iterrows():
                # 获取交易日
                if result["交易日"] is None:
                    result["交易日"] = str(row['交易日'])
                
                # 计算净流入
                net_inflow = float(row['成交净买额']) if pd.notna(row['成交净买额']) else 0.0
                total_net_inflow += net_inflow
                
                # 添加详情
                detail = {
                    "板块": row['板块'],
                    "净买额": net_inflow,
                    "相关指数": row['相关指数'],
                    "指数涨跌幅": float(row['指数涨跌幅']) if pd.notna(row['指数涨跌幅']) else 0.0,
                    "上涨股票数": int(row['上涨数']) if pd.notna(row['上涨数']) else 0,
                    "下跌股票数": int(row['下跌数']) if pd.notna(row['下跌数']) else 0
                }
                result["港股通详情"].append(detail)
            
            result["南向资金总净流入"] = total_net_inflow
            
            # 添加市场表现汇总
            if result["港股通详情"]:
                hsi_change = result["港股通详情"][0]["指数涨跌幅"]  # 恒生指数涨跌幅
                total_up = sum([d["上涨股票数"] for d in result["港股通详情"]])
                total_down = sum([d["下跌股票数"] for d in result["港股通详情"]])
                
                result["市场表现"] = {
                    "恒生指数涨跌幅": hsi_change,
                    "上涨股票总数": total_up,
                    "下跌股票总数": total_down,
                    "上涨下跌比": round(total_up / total_down, 2) if total_down > 0 else "N/A"
                }
            
            logger.info(f"成功获取南向资金数据: 总净流入 {total_net_inflow:.2f} 亿元")
            return result
            
        except Exception as e:
            logger.error(f"获取南向资金数据时出错: {str(e)}")
            return None
    
    def get_northbound_flow(self):
        """
        获取北向资金流向数据（外资流入内地）作为对比
        
        Returns:
            dict: 北向资金流向信息
        """
        try:
            logger.info("正在获取北向资金流向数据...")
            
            # 获取沪深港通资金流向汇总数据
            data = ak.stock_hsgt_fund_flow_summary_em()
            
            if data is None or data.empty:
                return None
            
            # 筛选北向资金
            northbound_data = data[data['资金方向'] == '北向'].copy()
            
            if northbound_data.empty:
                return None
            
            result = {
                "北向资金总净流入": 0.0,
                "详情": []
            }
            
            total_net_inflow = 0.0
            
            for _, row in northbound_data.iterrows():
                net_inflow = float(row['成交净买额']) if pd.notna(row['成交净买额']) else 0.0
                total_net_inflow += net_inflow
                
                detail = {
                    "板块": row['板块'],
                    "净买额": net_inflow,
                    "相关指数": row['相关指数'],
                    "指数涨跌幅": float(row['指数涨跌幅']) if pd.notna(row['指数涨跌幅']) else 0.0
                }
                result["详情"].append(detail)
            
            result["北向资金总净流入"] = total_net_inflow
            
            logger.info(f"成功获取北向资金数据: 总净流入 {total_net_inflow:.2f} 亿元")
            return result
            
        except Exception as e:
            logger.error(f"获取北向资金数据时出错: {str(e)}")
            return None
    
    def get_capital_flow_summary(self):
        """
        获取资金流向汇总信息
        
        Returns:
            dict: 资金流向汇总
        """
        southbound = self.get_latest_southbound_flow()
        northbound = self.get_northbound_flow()
        
        summary = {
            "数据更新时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "南向资金": southbound,
            "北向资金": northbound
        }
        
        # 计算净流向
        if southbound and northbound:
            south_total = southbound.get("南向资金总净流入", 0)
            north_total = northbound.get("北向资金总净流入", 0)
            
            summary["资金流向对比"] = {
                "南向资金净流入": south_total,
                "北向资金净流入": north_total,
                "净流向差额": south_total - north_total,
                "说明": "正值表示更多资金流入香港，负值表示更多资金流入内地"
            }
        
        return summary

def main():
    """主函数演示"""
    print("=" * 60)
    print("香港外资流入数据获取器")
    print("=" * 60)
    
    # 创建API实例
    api = SimpleHKForeignCapital()
    
    # 获取完整汇总信息
    summary = api.get_capital_flow_summary()
    
    if summary:
        print(f"\n📅 数据更新时间: {summary['数据更新时间']}")
        
        # 显示南向资金信息
        if summary['南向资金']:
            south_data = summary['南向资金']
            print(f"\n🔽 南向资金流入香港:")
            print(f"   交易日: {south_data['交易日']}")
            print(f"   总净流入: {south_data['南向资金总净流入']:.2f} 亿元")
            
            print(f"\n   港股通详情:")
            for detail in south_data['港股通详情']:
                flow_direction = "流入" if detail['净买额'] > 0 else "流出"
                print(f"     {detail['板块']}: {detail['净买额']:.2f} 亿元 ({flow_direction})")
            
            if south_data['市场表现']:
                market = south_data['市场表现']
                print(f"\n   市场表现:")
                print(f"     恒生指数: {market['恒生指数涨跌幅']:+.2f}%")
                print(f"     上涨股票: {market['上涨股票总数']} 只")
                print(f"     下跌股票: {market['下跌股票总数']} 只")
                print(f"     上涨/下跌比: {market['上涨下跌比']}")
        
        # 显示北向资金信息
        if summary['北向资金']:
            north_data = summary['北向资金']
            print(f"\n🔼 北向资金流入内地:")
            print(f"   总净流入: {north_data['北向资金总净流入']:.2f} 亿元")
            
            print(f"\n   详情:")
            for detail in north_data['详情']:
                flow_direction = "流入" if detail['净买额'] > 0 else "流出"
                print(f"     {detail['板块']}: {detail['净买额']:.2f} 亿元 ({flow_direction})")
                print(f"       {detail['相关指数']}: {detail['指数涨跌幅']:+.2f}%")
        
        # 显示对比分析
        if '资金流向对比' in summary:
            comparison = summary['资金流向对比']
            print(f"\n⚖️  资金流向对比:")
            print(f"   南向资金净流入: {comparison['南向资金净流入']:+.2f} 亿元")
            print(f"   北向资金净流入: {comparison['北向资金净流入']:+.2f} 亿元")
            print(f"   净流向差额: {comparison['净流向差额']:+.2f} 亿元")
            print(f"   {comparison['说明']}")
        
        # 保存数据到JSON文件
        output_file = f"hk_foreign_capital_summary_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print(f"\n💾 数据已保存到: {output_file}")
        
    else:
        print("\n❌ 未能获取到资金流向数据")
    
    print("\n✅ 数据获取完成！")

if __name__ == "__main__":
    main()
