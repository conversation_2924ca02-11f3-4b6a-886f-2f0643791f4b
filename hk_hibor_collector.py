#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港HIBOR利率数据收集器
使用akshare和东方财富API获取香港银行同业拆借利率数据
"""

import akshare as ak
import pandas as pd
import requests
import json
import time
from datetime import datetime, timedelta
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HKHiborCollector:
    def __init__(self):
        """初始化HIBOR数据收集器"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # HIBOR期限列表
        self.hibor_terms = [
            "隔夜",    # Overnight
            "1周",     # 1 Week
            "2周",     # 2 Weeks
            "1月",     # 1 Month
            "2月",     # 2 Months
            "3月",     # 3 Months
            "6月",     # 6 Months
            "12月"     # 12 Months
        ]
        
        # 英文对照
        self.term_mapping = {
            "隔夜": "Overnight",
            "1周": "1W",
            "2周": "2W", 
            "1月": "1M",
            "2月": "2M",
            "3月": "3M",
            "6月": "6M",
            "12月": "12M"
        }
    
    def get_hibor_data_akshare(self, term="1月"):
        """
        使用akshare获取香港HIBOR利率数据
        
        Args:
            term: 期限，可选值：隔夜、1周、2周、1月、2月、3月、6月、12月
        
        Returns:
            pandas.DataFrame: HIBOR利率数据
        """
        try:
            logger.info(f"正在获取香港HIBOR {term} 利率数据...")
            
            # 使用akshare的rate_interbank函数获取香港银行同业拆借利率
            hibor_data = ak.rate_interbank(
                market="香港银行同业拆借市场",
                symbol="Hibor港币",  # 香港银行同业拆借利率
                indicator=term
            )
            
            if hibor_data is not None and not hibor_data.empty:
                logger.info(f"成功获取 {len(hibor_data)} 条 HIBOR {term} 数据")
                
                # 数据清理和格式化
                hibor_data = hibor_data.copy()
                hibor_data['报告日'] = pd.to_datetime(hibor_data['报告日'])
                hibor_data['利率'] = pd.to_numeric(hibor_data['利率'], errors='coerce')
                hibor_data['涨跌'] = pd.to_numeric(hibor_data['涨跌'], errors='coerce')
                
                # 按日期排序
                hibor_data = hibor_data.sort_values('报告日').reset_index(drop=True)
                
                # 添加期限列
                hibor_data['期限'] = term
                hibor_data['期限_英文'] = self.term_mapping.get(term, term)
                
                return hibor_data
            else:
                logger.warning(f"未获取到 HIBOR {term} 数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取 HIBOR {term} 数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def get_all_hibor_terms(self):
        """
        获取所有期限的HIBOR利率数据
        
        Returns:
            pandas.DataFrame: 包含所有期限的HIBOR数据
        """
        all_data = []
        
        for term in self.hibor_terms:
            logger.info(f"正在获取 HIBOR {term} 数据...")
            
            # 获取单个期限的数据
            term_data = self.get_hibor_data_akshare(term)
            
            if not term_data.empty:
                all_data.append(term_data)
                logger.info(f"✅ HIBOR {term}: {len(term_data)} 条记录")
            else:
                logger.warning(f"❌ HIBOR {term}: 无数据")
            
            # 添加延迟避免请求过于频繁
            time.sleep(1)
        
        if all_data:
            # 合并所有数据
            combined_data = pd.concat(all_data, ignore_index=True)
            logger.info(f"总共获取 {len(combined_data)} 条 HIBOR 数据")
            return combined_data
        else:
            logger.warning("未获取到任何 HIBOR 数据")
            return pd.DataFrame()
    
    def save_hibor_data(self, data, filename="hk_hibor_rates.csv"):
        """
        保存HIBOR数据到CSV文件
        
        Args:
            data: pandas.DataFrame, HIBOR数据
            filename: str, 文件名
        """
        if data.empty:
            logger.warning("数据为空，无法保存")
            return
        
        filepath = os.path.join(self.data_dir, filename)
        
        try:
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"HIBOR数据已保存到: {filepath}")
            
            # 显示数据概览
            logger.info(f"数据概览:")
            logger.info(f"  总记录数: {len(data)}")
            logger.info(f"  日期范围: {data['报告日'].min()} 到 {data['报告日'].max()}")
            logger.info(f"  包含期限: {', '.join(data['期限'].unique())}")
            
            # 显示最新数据
            if len(data) > 0:
                latest_data = data.groupby('期限').last().reset_index()
                logger.info(f"最新利率数据:")
                for _, row in latest_data.iterrows():
                    logger.info(f"  {row['期限']}: {row['利率']:.4f}% ({row['报告日'].strftime('%Y-%m-%d')})")
            
        except Exception as e:
            logger.error(f"保存数据时出错: {str(e)}")
    
    def get_latest_hibor_rates(self):
        """
        获取最新的HIBOR利率数据（所有期限）
        
        Returns:
            pandas.DataFrame: 最新的HIBOR利率数据
        """
        logger.info("正在获取最新的香港HIBOR利率数据...")
        
        # 获取所有期限的数据
        all_data = self.get_all_hibor_terms()
        
        if not all_data.empty:
            # 保存完整数据
            self.save_hibor_data(all_data, "hk_hibor_rates_full.csv")
            
            # 获取每个期限的最新数据
            latest_data = all_data.groupby('期限').last().reset_index()
            
            # 保存最新数据
            self.save_hibor_data(latest_data, "hk_hibor_rates_latest.csv")
            
            return latest_data
        else:
            logger.error("未能获取到HIBOR数据")
            return pd.DataFrame()

def main():
    """主函数"""
    print("=" * 60)
    print("香港HIBOR利率数据收集器")
    print("=" * 60)
    
    # 创建收集器实例
    collector = HKHiborCollector()
    
    # 获取最新的HIBOR利率数据
    latest_rates = collector.get_latest_hibor_rates()
    
    if not latest_rates.empty:
        print("\n✅ 数据收集完成！")
        print(f"共获取 {len(latest_rates)} 个期限的最新HIBOR利率")
        
        # 显示结果摘要
        print("\n📊 最新HIBOR利率:")
        print("-" * 40)
        for _, row in latest_rates.iterrows():
            print(f"{row['期限']:>6}: {row['利率']:>8.4f}% ({row['报告日'].strftime('%Y-%m-%d')})")
    else:
        print("\n❌ 数据收集失败")

if __name__ == "__main__":
    main()
