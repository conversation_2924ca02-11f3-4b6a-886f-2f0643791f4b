#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港HIBOR利率API接口
提供便捷的HIBOR利率数据获取和查询功能
"""

import akshare as ak
import pandas as pd
import json
from datetime import datetime, timedelta
import logging
from typing import Optional, List, Dict, Union

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HiborAPI:
    """香港HIBOR利率API类"""
    
    def __init__(self):
        """初始化API"""
        self.available_terms = {
            "隔夜": "Overnight",
            "1周": "1W", 
            "2周": "2W",
            "1月": "1M",
            "2月": "2M", 
            "3月": "3M",
            "6月": "6M",
            "12月": "12M"
        }
        
        # 常用期限的英文名称映射
        self.english_terms = {
            "overnight": "隔夜",
            "1w": "1周",
            "1week": "1周", 
            "2w": "2周",
            "2week": "2周",
            "1m": "1月",
            "1month": "1月",
            "2m": "2月", 
            "2month": "2月",
            "3m": "3月",
            "3month": "3月",
            "6m": "6月",
            "6month": "6月",
            "12m": "12月",
            "1y": "12月",
            "1year": "12月"
        }
    
    def get_hibor_rate(self, term: str = "3月", days: int = 30) -> Optional[pd.DataFrame]:
        """
        获取指定期限的HIBOR利率数据
        
        Args:
            term: 期限，支持中文（如"3月"）或英文（如"3m", "3month"）
            days: 获取最近多少天的数据，默认30天
            
        Returns:
            pandas.DataFrame: HIBOR利率数据，包含日期、利率、涨跌等信息
        """
        # 标准化期限名称
        normalized_term = self._normalize_term(term)
        if not normalized_term:
            logger.error(f"不支持的期限: {term}")
            return None
        
        try:
            logger.info(f"正在获取 HIBOR {normalized_term} 利率数据...")
            
            # 使用akshare获取数据
            data = ak.rate_interbank(
                market="香港银行同业拆借市场",
                symbol="Hibor港币",
                indicator=normalized_term
            )
            
            if data is None or data.empty:
                logger.warning(f"未获取到 HIBOR {normalized_term} 数据")
                return None
            
            # 数据处理
            data = data.copy()
            data['报告日'] = pd.to_datetime(data['报告日'])
            data['利率'] = pd.to_numeric(data['利率'], errors='coerce')
            data['涨跌'] = pd.to_numeric(data['涨跌'], errors='coerce')
            
            # 按日期排序并筛选最近的数据
            data = data.sort_values('报告日', ascending=False)
            if days > 0:
                cutoff_date = datetime.now() - timedelta(days=days)
                data = data[data['报告日'] >= cutoff_date]
            
            # 添加期限信息
            data['期限'] = normalized_term
            data['期限_英文'] = self.available_terms.get(normalized_term, normalized_term)
            
            logger.info(f"成功获取 {len(data)} 条 HIBOR {normalized_term} 数据")
            return data.reset_index(drop=True)
            
        except Exception as e:
            logger.error(f"获取 HIBOR {normalized_term} 数据时出错: {str(e)}")
            return None
    
    def get_latest_rate(self, term: str = "3月") -> Optional[Dict]:
        """
        获取指定期限的最新HIBOR利率
        
        Args:
            term: 期限
            
        Returns:
            dict: 包含最新利率信息的字典
        """
        data = self.get_hibor_rate(term, days=7)  # 获取最近7天数据
        
        if data is None or data.empty:
            return None
        
        latest = data.iloc[0]
        return {
            "期限": latest['期限'],
            "期限_英文": latest['期限_英文'],
            "利率": float(latest['利率']),
            "涨跌": float(latest['涨跌']) if pd.notna(latest['涨跌']) else 0.0,
            "报告日": latest['报告日'].strftime('%Y-%m-%d'),
            "更新时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def get_all_latest_rates(self) -> List[Dict]:
        """
        获取所有期限的最新HIBOR利率
        
        Returns:
            list: 包含所有期限最新利率的列表
        """
        all_rates = []
        
        for term in self.available_terms.keys():
            logger.info(f"获取 {term} 最新利率...")
            rate_info = self.get_latest_rate(term)
            
            if rate_info:
                all_rates.append(rate_info)
            else:
                logger.warning(f"未能获取 {term} 利率数据")
        
        # 按利率排序
        all_rates.sort(key=lambda x: x['利率'])
        
        return all_rates
    
    def get_yield_curve(self) -> Optional[Dict]:
        """
        获取HIBOR收益率曲线数据
        
        Returns:
            dict: 收益率曲线数据
        """
        logger.info("正在构建HIBOR收益率曲线...")
        
        # 期限排序（从短到长）
        term_order = ['隔夜', '1周', '2周', '1月', '2月', '3月', '6月', '12月']
        
        curve_data = []
        for term in term_order:
            rate_info = self.get_latest_rate(term)
            if rate_info:
                curve_data.append({
                    "期限": term,
                    "期限_英文": rate_info["期限_英文"],
                    "利率": rate_info["利率"],
                    "报告日": rate_info["报告日"]
                })
        
        if not curve_data:
            logger.error("无法获取收益率曲线数据")
            return None
        
        return {
            "收益率曲线": curve_data,
            "更新时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "数据点数": len(curve_data)
        }
    
    def compare_rates(self, terms: List[str], days: int = 30) -> Optional[pd.DataFrame]:
        """
        比较多个期限的HIBOR利率
        
        Args:
            terms: 期限列表
            days: 比较最近多少天的数据
            
        Returns:
            pandas.DataFrame: 比较结果
        """
        all_data = []
        
        for term in terms:
            data = self.get_hibor_rate(term, days)
            if data is not None and not data.empty:
                all_data.append(data)
        
        if not all_data:
            logger.error("未能获取任何比较数据")
            return None
        
        # 合并数据
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 透视表格式，便于比较
        pivot_data = combined_data.pivot_table(
            index='报告日', 
            columns='期限', 
            values='利率', 
            aggfunc='first'
        ).reset_index()
        
        return pivot_data.sort_values('报告日', ascending=False)
    
    def _normalize_term(self, term: str) -> Optional[str]:
        """标准化期限名称"""
        term = term.lower().strip()
        
        # 如果是英文，转换为中文
        if term in self.english_terms:
            return self.english_terms[term]
        
        # 如果是中文，检查是否支持
        for chinese_term in self.available_terms.keys():
            if term == chinese_term.lower() or term == chinese_term:
                return chinese_term
        
        return None
    
    def get_supported_terms(self) -> Dict[str, str]:
        """获取支持的期限列表"""
        return self.available_terms.copy()

def main():
    """演示API使用"""
    print("=" * 60)
    print("香港HIBOR利率API演示")
    print("=" * 60)
    
    # 创建API实例
    api = HiborAPI()
    
    # 1. 获取支持的期限
    print("\n📋 支持的期限:")
    terms = api.get_supported_terms()
    for chinese, english in terms.items():
        print(f"  {chinese} ({english})")
    
    # 2. 获取3月期HIBOR最新利率
    print("\n📊 3月期HIBOR最新利率:")
    latest_3m = api.get_latest_rate("3月")
    if latest_3m:
        print(json.dumps(latest_3m, indent=2, ensure_ascii=False))
    
    # 3. 获取所有期限的最新利率
    print("\n📈 所有期限最新利率:")
    all_latest = api.get_all_latest_rates()
    for rate in all_latest:
        print(f"  {rate['期限']:>6}: {rate['利率']:>8.4f}% ({rate['报告日']})")
    
    # 4. 获取收益率曲线
    print("\n📉 HIBOR收益率曲线:")
    yield_curve = api.get_yield_curve()
    if yield_curve:
        for point in yield_curve["收益率曲线"]:
            print(f"  {point['期限']:>6}: {point['利率']:>8.4f}%")
    
    # 5. 比较不同期限（最近7天）
    print("\n🔍 期限比较 (最近7天):")
    comparison = api.compare_rates(["隔夜", "1周", "1月", "3月"], days=7)
    if comparison is not None:
        print(comparison.head().to_string(index=False, float_format='%.4f'))
    
    print("\n✅ API演示完成！")

if __name__ == "__main__":
    main()
