#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港外资流入数据收集器
获取外国投资者流入香港股市的资金数据
"""

import akshare as ak
import pandas as pd
import requests
import json
import time
from datetime import datetime, timedelta
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HKForeignCapitalCollector:
    def __init__(self):
        """初始化外资流入数据收集器"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 东方财富API基础URL
        self.eastmoney_base_url = "https://datacenter.eastmoney.com/securities/api/data/get"
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*'
        }
    
    def get_hk_foreign_inflow_akshare(self):
        """
        使用akshare获取香港外资流入数据
        尝试多个可能的接口
        """
        try:
            logger.info("正在使用akshare获取香港外资流入数据...")
            
            # 尝试获取港股通资金流向数据（南向资金）
            try:
                logger.info("尝试获取港股通南向资金流向数据...")
                southbound_data = ak.stock_connect_fund_flow_em()
                if southbound_data is not None and not southbound_data.empty:
                    logger.info(f"成功获取港股通资金流向数据: {len(southbound_data)} 条记录")
                    return southbound_data
            except Exception as e:
                logger.warning(f"获取港股通资金流向数据失败: {str(e)}")
            
            # 尝试获取沪深港通资金流向
            try:
                logger.info("尝试获取沪深港通资金流向数据...")
                connect_data = ak.stock_hsgt_fund_flow_summary_em()
                if connect_data is not None and not connect_data.empty:
                    logger.info(f"成功获取沪深港通资金流向数据: {len(connect_data)} 条记录")
                    return connect_data
            except Exception as e:
                logger.warning(f"获取沪深港通资金流向数据失败: {str(e)}")
            
            # 尝试获取港股通历史资金流向
            try:
                logger.info("尝试获取港股通历史资金流向数据...")
                hist_data = ak.stock_hsgt_hist_em()
                if hist_data is not None and not hist_data.empty:
                    logger.info(f"成功获取港股通历史资金流向数据: {len(hist_data)} 条记录")
                    return hist_data
            except Exception as e:
                logger.warning(f"获取港股通历史资金流向数据失败: {str(e)}")
            
            logger.warning("所有akshare接口都无法获取到数据")
            return None
            
        except Exception as e:
            logger.error(f"使用akshare获取数据时出错: {str(e)}")
            return None
    
    def get_hk_foreign_inflow_eastmoney(self):
        """
        使用东方财富API获取香港外资流入数据
        """
        try:
            logger.info("正在使用东方财富API获取香港外资流入数据...")
            
            # 港股通资金流向API
            params = {
                'type': 'RPT_MUTUAL_MARKET_STA',
                'sty': 'ALL',
                'source': 'WEB',
                'p': '1',
                'ps': '500',
                'st': 'TRADE_DATE',
                'sr': '-1'
            }
            
            response = requests.get(self.eastmoney_base_url, params=params, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'result' in data and 'data' in data['result']:
                    records = data['result']['data']
                    
                    if records:
                        df = pd.DataFrame(records)
                        logger.info(f"成功获取东方财富港股通资金流向数据: {len(df)} 条记录")
                        return df
                    else:
                        logger.warning("东方财富API返回空数据")
                else:
                    logger.warning("东方财富API响应格式异常")
            else:
                logger.warning(f"东方财富API请求失败: {response.status_code}")
            
            return None
            
        except Exception as e:
            logger.error(f"使用东方财富API获取数据时出错: {str(e)}")
            return None
    
    def get_hk_adr_data(self):
        """
        获取香港ADR相关数据作为外资流入的参考指标
        """
        try:
            logger.info("正在获取香港ADR相关数据...")
            
            # 尝试获取恒生指数相关的ADR数据
            # 这里可以作为外资投资香港的间接指标
            
            return None
            
        except Exception as e:
            logger.error(f"获取ADR数据时出错: {str(e)}")
            return None
    
    def get_hk_etf_flows(self):
        """
        获取香港ETF资金流向数据
        """
        try:
            logger.info("正在获取香港ETF资金流向数据...")
            
            # 尝试获取香港相关ETF的资金流向
            # 这可以反映外资对香港市场的投资情况
            
            return None
            
        except Exception as e:
            logger.error(f"获取ETF流向数据时出错: {str(e)}")
            return None
    
    def collect_all_foreign_capital_data(self):
        """
        收集所有可用的外资流入香港数据
        """
        logger.info("开始收集香港外资流入数据...")
        
        all_data = {}
        
        # 1. 尝试akshare数据源
        akshare_data = self.get_hk_foreign_inflow_akshare()
        if akshare_data is not None:
            all_data['akshare_data'] = akshare_data
        
        # 2. 尝试东方财富数据源
        eastmoney_data = self.get_hk_foreign_inflow_eastmoney()
        if eastmoney_data is not None:
            all_data['eastmoney_data'] = eastmoney_data
        
        # 3. 尝试ADR数据
        adr_data = self.get_hk_adr_data()
        if adr_data is not None:
            all_data['adr_data'] = adr_data
        
        # 4. 尝试ETF流向数据
        etf_data = self.get_hk_etf_flows()
        if etf_data is not None:
            all_data['etf_data'] = etf_data
        
        return all_data
    
    def save_data(self, data_dict, base_filename="hk_foreign_capital"):
        """
        保存收集到的数据
        """
        if not data_dict:
            logger.warning("没有数据需要保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for data_type, data in data_dict.items():
            if data is not None and not data.empty:
                filename = f"{base_filename}_{data_type}_{timestamp}.csv"
                filepath = os.path.join(self.data_dir, filename)
                
                try:
                    data.to_csv(filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"数据已保存: {filepath}")
                    
                    # 显示数据概览
                    logger.info(f"{data_type} 数据概览:")
                    logger.info(f"  记录数: {len(data)}")
                    logger.info(f"  列名: {', '.join(data.columns.tolist())}")
                    
                    if len(data) > 0:
                        logger.info(f"  数据示例:")
                        logger.info(f"    {data.head(1).to_string(index=False)}")
                    
                except Exception as e:
                    logger.error(f"保存数据时出错: {str(e)}")
    
    def analyze_foreign_capital_trends(self, data):
        """
        分析外资流入趋势
        """
        if data is None or data.empty:
            logger.warning("没有数据可供分析")
            return
        
        logger.info("分析外资流入趋势...")
        
        # 这里可以添加具体的分析逻辑
        # 例如计算净流入、流入趋势等
        
        logger.info(f"数据分析完成，共分析 {len(data)} 条记录")

def main():
    """主函数"""
    print("=" * 60)
    print("香港外资流入数据收集器")
    print("=" * 60)
    
    # 创建收集器实例
    collector = HKForeignCapitalCollector()
    
    # 收集所有可用数据
    all_data = collector.collect_all_foreign_capital_data()
    
    if all_data:
        print(f"\n✅ 成功收集到 {len(all_data)} 种类型的数据")
        
        # 保存数据
        collector.save_data(all_data)
        
        # 分析数据
        for data_type, data in all_data.items():
            if data is not None:
                collector.analyze_foreign_capital_trends(data)
        
        print("\n📊 数据收集和分析完成！")
    else:
        print("\n❌ 未能收集到任何外资流入数据")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. API接口变更")
        print("3. 数据源暂时不可用")

if __name__ == "__main__":
    main()
