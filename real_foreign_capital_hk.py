#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的外资流入香港数据收集器
获取国际投资者流入香港的真实外资数据
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import logging
import os
from bs4 import BeautifulSoup
import akshare as ak

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealForeignCapitalHK:
    """真正的外资流入香港数据收集器"""
    
    def __init__(self):
        """初始化"""
        self.data_dir = "data_files"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7'
        }
    
    def get_hkma_data(self):
        """
        尝试从香港金管局获取外资流入数据
        """
        try:
            logger.info("正在尝试从香港金管局获取外资数据...")
            
            # 香港金管局统计数据API
            hkma_urls = [
                "https://api.hkma.gov.hk/public/market-data-and-statistics/monthly-statistical-bulletin/er-ir/er-eeri-ir",
                "https://api.hkma.gov.hk/public/market-data-and-statistics/monthly-statistical-bulletin/er-ir/er-eeri",
                "https://www.hkma.gov.hk/eng/data-publications-and-research/data-and-statistics/"
            ]
            
            for url in hkma_urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"成功从香港金管局获取数据: {len(data)} 条记录")
                        return pd.DataFrame(data)
                except Exception as e:
                    logger.warning(f"香港金管局API {url} 访问失败: {str(e)}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"获取香港金管局数据时出错: {str(e)}")
            return None
    
    def get_hkex_foreign_investor_data(self):
        """
        尝试从港交所获取外国投资者数据
        """
        try:
            logger.info("正在尝试从港交所获取外国投资者数据...")
            
            # 港交所可能的API端点
            hkex_urls = [
                "https://www.hkex.com.hk/eng/stat/smstat/daystat/d_turnover.htm",
                "https://www.hkex.com.hk/eng/stat/smstat/daystat/d_foreign.htm"
            ]
            
            for url in hkex_urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=30)
                    if response.status_code == 200:
                        # 尝试解析HTML页面
                        soup = BeautifulSoup(response.content, 'html.parser')
                        tables = soup.find_all('table')
                        
                        if tables:
                            # 尝试提取表格数据
                            for table in tables:
                                df = pd.read_html(str(table))[0]
                                if not df.empty:
                                    logger.info(f"从港交所获取到表格数据: {len(df)} 行")
                                    return df
                except Exception as e:
                    logger.warning(f"港交所数据获取失败: {str(e)}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"获取港交所数据时出错: {str(e)}")
            return None
    
    def get_epfr_like_data(self):
        """
        尝试获取类似EPFR的基金流向数据
        """
        try:
            logger.info("正在尝试获取国际基金流向数据...")
            
            # 尝试使用akshare获取相关的基金数据
            try:
                # 尝试获取香港相关ETF数据
                hk_etf_data = ak.fund_etf_category_sina(symbol="ETF基金")
                if hk_etf_data is not None and not hk_etf_data.empty:
                    # 筛选香港相关ETF
                    hk_related = hk_etf_data[hk_etf_data['基金简称'].str.contains('港|香港|恒生|H股', na=False)]
                    if not hk_related.empty:
                        logger.info(f"获取到香港相关ETF数据: {len(hk_related)} 只基金")
                        return hk_related
            except Exception as e:
                logger.warning(f"获取ETF数据失败: {str(e)}")
            
            return None
            
        except Exception as e:
            logger.error(f"获取基金流向数据时出错: {str(e)}")
            return None
    
    def get_foreign_exchange_data(self):
        """
        获取外汇相关数据作为外资流入的间接指标
        """
        try:
            logger.info("正在获取外汇相关数据...")
            
            # 尝试获取港币汇率数据
            try:
                # 获取美元兑港币汇率
                usd_hkd = ak.currency_boc_sina(symbol="美元", start_date="20240101", end_date="20241231")
                if usd_hkd is not None and not usd_hkd.empty:
                    logger.info(f"获取到美元兑港币汇率数据: {len(usd_hkd)} 条记录")
                    return usd_hkd
            except Exception as e:
                logger.warning(f"获取汇率数据失败: {str(e)}")
            
            return None
            
        except Exception as e:
            logger.error(f"获取外汇数据时出错: {str(e)}")
            return None
    
    def get_international_bond_flows(self):
        """
        尝试获取国际债券流向数据
        """
        try:
            logger.info("正在尝试获取国际债券流向数据...")
            
            # 这里可以尝试获取香港债券市场的外资参与数据
            # 由于缺乏直接API，这里返回None
            return None
            
        except Exception as e:
            logger.error(f"获取债券流向数据时出错: {str(e)}")
            return None
    
    def search_alternative_sources(self):
        """
        搜索其他可能的外资数据源
        """
        try:
            logger.info("正在搜索其他外资数据源...")
            
            # 尝试一些可能包含外资数据的API
            alternative_sources = [
                {
                    "name": "Wind万得数据",
                    "description": "专业金融数据提供商，可能有外资流向数据"
                },
                {
                    "name": "Bloomberg API",
                    "description": "彭博终端数据，包含机构投资者流向"
                },
                {
                    "name": "CEIC数据",
                    "description": "亚洲经济数据库，可能包含香港外资数据"
                },
                {
                    "name": "香港统计处",
                    "description": "香港政府统计部门，可能有国际收支数据"
                }
            ]
            
            return alternative_sources
            
        except Exception as e:
            logger.error(f"搜索替代数据源时出错: {str(e)}")
            return None
    
    def collect_all_available_data(self):
        """
        收集所有可用的外资相关数据
        """
        logger.info("开始收集真正的外资流入数据...")
        
        results = {
            "收集时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "数据源": {},
            "替代指标": {},
            "数据源建议": []
        }
        
        # 1. 尝试香港金管局数据
        hkma_data = self.get_hkma_data()
        if hkma_data is not None:
            results["数据源"]["香港金管局"] = {
                "状态": "成功",
                "记录数": len(hkma_data),
                "数据": hkma_data.to_dict('records')[:5]  # 只保存前5条作为示例
            }
        else:
            results["数据源"]["香港金管局"] = {"状态": "失败", "原因": "API访问失败或数据格式变更"}
        
        # 2. 尝试港交所数据
        hkex_data = self.get_hkex_foreign_investor_data()
        if hkex_data is not None:
            results["数据源"]["港交所"] = {
                "状态": "成功",
                "记录数": len(hkex_data),
                "数据": hkex_data.to_dict('records')[:5]
            }
        else:
            results["数据源"]["港交所"] = {"状态": "失败", "原因": "网页结构变更或访问限制"}
        
        # 3. 尝试基金流向数据
        fund_data = self.get_epfr_like_data()
        if fund_data is not None:
            results["替代指标"]["香港相关基金"] = {
                "状态": "成功",
                "记录数": len(fund_data),
                "说明": "香港相关ETF和基金数据，可间接反映外资兴趣"
            }
        else:
            results["替代指标"]["香港相关基金"] = {"状态": "失败"}
        
        # 4. 获取外汇数据
        fx_data = self.get_foreign_exchange_data()
        if fx_data is not None:
            results["替代指标"]["外汇数据"] = {
                "状态": "成功",
                "记录数": len(fx_data),
                "说明": "港币汇率变化可间接反映资金流向"
            }
        else:
            results["替代指标"]["外汇数据"] = {"状态": "失败"}
        
        # 5. 获取数据源建议
        alternative_sources = self.search_alternative_sources()
        if alternative_sources:
            results["数据源建议"] = alternative_sources
        
        return results
    
    def save_results(self, results):
        """保存收集结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"real_foreign_capital_hk_{timestamp}.json"
        filepath = os.path.join(self.data_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"结果已保存到: {filepath}")
        return filepath

def main():
    """主函数"""
    print("=" * 60)
    print("真正的外资流入香港数据收集器")
    print("=" * 60)
    
    collector = RealForeignCapitalHK()
    
    # 收集所有可用数据
    results = collector.collect_all_available_data()
    
    # 显示结果
    print(f"\n📅 数据收集时间: {results['收集时间']}")
    
    print(f"\n🏦 官方数据源尝试结果:")
    for source, info in results["数据源"].items():
        status_icon = "✅" if info["状态"] == "成功" else "❌"
        print(f"   {status_icon} {source}: {info['状态']}")
        if info["状态"] == "成功":
            print(f"      记录数: {info['记录数']}")
        else:
            print(f"      原因: {info.get('原因', '未知')}")
    
    print(f"\n📊 替代指标数据:")
    for indicator, info in results["替代指标"].items():
        status_icon = "✅" if info["状态"] == "成功" else "❌"
        print(f"   {status_icon} {indicator}: {info['状态']}")
        if info["状态"] == "成功":
            print(f"      {info.get('说明', '')}")
    
    print(f"\n💡 推荐的专业数据源:")
    for i, source in enumerate(results["数据源建议"], 1):
        print(f"   {i}. {source['name']}")
        print(f"      {source['description']}")
    
    # 保存结果
    filepath = collector.save_results(results)
    print(f"\n💾 详细结果已保存到: {filepath}")
    
    print(f"\n⚠️  重要说明:")
    print("   真正的外资流入数据通常需要:")
    print("   1. 专业金融数据服务商（如Wind、Bloomberg）")
    print("   2. 官方统计机构的付费API")
    print("   3. 机构投资者报告和披露数据")
    print("   4. 国际收支平衡表数据")
    
    print(f"\n✅ 数据收集完成！")

if __name__ == "__main__":
    main()
