#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势可视化脚本
专门展示HIBOR利率和外资流入的变化趋势
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import os
import akshare as ak
import logging

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrendVisualizer:
    """趋势可视化器"""
    
    def __init__(self):
        """初始化"""
        self.data_dir = "data_files"
        self.output_dir = "trend_charts"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def get_hibor_historical_trends(self, days=90):
        """获取HIBOR历史趋势数据"""
        try:
            logger.info(f"正在获取最近{days}天的HIBOR趋势数据...")
            
            # 可用的期限
            terms = ["隔夜", "1周", "2周", "1月", "2月", "3月", "6月"]
            all_data = []
            
            for term in terms:
                try:
                    # 获取单个期限的历史数据
                    data = ak.rate_interbank(
                        market="香港银行同业拆借市场",
                        symbol="Hibor港币",
                        indicator=term
                    )
                    
                    if data is not None and not data.empty:
                        data = data.copy()
                        data['报告日'] = pd.to_datetime(data['报告日'])
                        data['利率'] = pd.to_numeric(data['利率'], errors='coerce')
                        data['期限'] = term
                        
                        # 筛选最近的数据
                        cutoff_date = datetime.now() - timedelta(days=days)
                        recent_data = data[data['报告日'] >= cutoff_date]
                        
                        if not recent_data.empty:
                            all_data.append(recent_data)
                            logger.info(f"获取到 {term} 期限数据: {len(recent_data)} 条记录")
                    
                    # 添加延迟避免请求过频
                    import time
                    time.sleep(1)
                    
                except Exception as e:
                    logger.warning(f"获取 {term} 期限数据失败: {str(e)}")
                    continue
            
            if all_data:
                combined_data = pd.concat(all_data, ignore_index=True)
                logger.info(f"总共获取 {len(combined_data)} 条HIBOR历史数据")
                return combined_data
            
            return None
            
        except Exception as e:
            logger.error(f"获取HIBOR历史数据时出错: {str(e)}")
            return None
    
    def get_capital_flow_trends(self, days=30):
        """获取资金流向趋势数据"""
        try:
            logger.info(f"正在获取最近{days}天的资金流向趋势...")
            
            # 获取沪深港通历史数据
            hist_data = ak.stock_hsgt_hist_em()
            
            if hist_data is None or hist_data.empty:
                logger.warning("未获取到资金流向历史数据")
                return None
            
            # 数据处理
            hist_data['交易日期'] = pd.to_datetime(hist_data['交易日期'])
            
            # 筛选最近的数据
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_data = hist_data[hist_data['交易日期'] >= cutoff_date].copy()
            
            # 数值列转换
            numeric_columns = ['沪股通净流入', '深股通净流入', '港股通净流入', '北向资金', '南向资金']
            for col in numeric_columns:
                if col in recent_data.columns:
                    recent_data[col] = pd.to_numeric(recent_data[col], errors='coerce')
            
            logger.info(f"获取到 {len(recent_data)} 条资金流向历史数据")
            return recent_data
            
        except Exception as e:
            logger.error(f"获取资金流向趋势数据时出错: {str(e)}")
            return None
    
    def plot_hibor_trends(self, days=90):
        """绘制HIBOR利率趋势图"""
        hibor_data = self.get_hibor_historical_trends(days)
        
        if hibor_data is None or hibor_data.empty:
            logger.warning("没有HIBOR数据可供绘制趋势图")
            return
        
        # 创建图表
        plt.figure(figsize=(15, 10))
        
        # 定义颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2']
        
        # 绘制每个期限的趋势
        latest_points = []
        for i, term in enumerate(sorted(hibor_data['期限'].unique())):
            term_data = hibor_data[hibor_data['期限'] == term].copy()
            term_data = term_data.sort_values('报告日')
            
            if len(term_data) > 1:
                color = colors[i % len(colors)]
                
                # 绘制趋势线
                plt.plot(term_data['报告日'], term_data['利率'], 
                        label=f'{term}', linewidth=2.5, alpha=0.8, color=color, marker='o', markersize=3)
                
                # 记录最新点
                if not term_data.empty:
                    latest = term_data.iloc[-1]
                    latest_points.append({
                        '期限': term,
                        '日期': latest['报告日'],
                        '利率': latest['利率'],
                        '颜色': color
                    })
        
        # 标识最新利率点
        for point in latest_points:
            plt.scatter(point['日期'], point['利率'], 
                       color=point['颜色'], s=120, zorder=5, edgecolors='white', linewidth=2)
            
            # 添加最新利率标签
            plt.annotate(f'{point["利率"]:.3f}%', 
                        xy=(point['日期'], point['利率']),
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.4', facecolor=point['颜色'], alpha=0.8, edgecolor='white'),
                        fontsize=10, fontweight='bold', color='white',
                        ha='left', va='bottom')
        
        plt.title(f'香港HIBOR利率变化趋势 (最近{days}天)', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('利率 (%)', fontsize=12)
        plt.legend(loc='upper left', frameon=True, fancybox=True, shadow=True, fontsize=10)
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.xticks(rotation=45)
        
        # 设置y轴范围
        if latest_points:
            all_rates = [p['利率'] for p in latest_points]
            y_min = min(all_rates) * 0.8
            y_max = max(all_rates) * 1.2
            plt.ylim(y_min, y_max)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = os.path.join(self.output_dir, f"hibor_trends_{days}days.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        logger.info(f"HIBOR趋势图已保存: {output_file}")
        
        plt.show()
    
    def plot_capital_flow_trends(self, days=30):
        """绘制资金流向趋势图"""
        flow_data = self.get_capital_flow_trends(days)
        
        if flow_data is None or flow_data.empty:
            logger.warning("没有资金流向数据可供绘制趋势图")
            return
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        # 上图：南向资金趋势
        if '南向资金' in flow_data.columns:
            south_data = flow_data[['交易日期', '南向资金']].dropna()
            
            if not south_data.empty:
                # 绘制柱状图
                colors = ['green' if x > 0 else 'red' for x in south_data['南向资金']]
                ax1.bar(south_data['交易日期'], south_data['南向资金'], 
                       color=colors, alpha=0.7, width=0.8)
                
                # 添加零线
                ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
                
                # 添加移动平均线
                if len(south_data) >= 5:
                    south_data = south_data.sort_values('交易日期')
                    south_data['MA5'] = south_data['南向资金'].rolling(window=5).mean()
                    ax1.plot(south_data['交易日期'], south_data['MA5'], 
                            color='blue', linewidth=3, label='5日移动平均', alpha=0.8)
                    ax1.legend()
                
                # 标识最新数据点
                if not south_data.empty:
                    latest = south_data.iloc[-1]
                    ax1.scatter(latest['交易日期'], latest['南向资金'], 
                              color='orange', s=150, zorder=5, edgecolors='white', linewidth=2)
                    
                    # 添加最新数值标签
                    ax1.annotate(f'{latest["南向资金"]:.1f}亿', 
                                xy=(latest['交易日期'], latest['南向资金']),
                                xytext=(10, 15), textcoords='offset points',
                                bbox=dict(boxstyle='round,pad=0.4', facecolor='orange', alpha=0.8),
                                fontsize=11, fontweight='bold', color='white',
                                ha='left', va='bottom')
        
        ax1.set_title(f'南向资金流入香港趋势 (最近{days}天)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('资金净流入 (亿元)', fontsize=12)
        ax1.grid(True, alpha=0.3, linestyle='--')
        ax1.tick_params(axis='x', rotation=45)
        
        # 下图：北向资金趋势
        if '北向资金' in flow_data.columns:
            north_data = flow_data[['交易日期', '北向资金']].dropna()
            
            if not north_data.empty:
                # 绘制柱状图
                colors = ['green' if x > 0 else 'red' for x in north_data['北向资金']]
                ax2.bar(north_data['交易日期'], north_data['北向资金'], 
                       color=colors, alpha=0.7, width=0.8)
                
                # 添加零线
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
                
                # 添加移动平均线
                if len(north_data) >= 5:
                    north_data = north_data.sort_values('交易日期')
                    north_data['MA5'] = north_data['北向资金'].rolling(window=5).mean()
                    ax2.plot(north_data['交易日期'], north_data['MA5'], 
                            color='purple', linewidth=3, label='5日移动平均', alpha=0.8)
                    ax2.legend()
                
                # 标识最新数据点
                if not north_data.empty:
                    latest = north_data.iloc[-1]
                    ax2.scatter(latest['交易日期'], latest['北向资金'], 
                              color='orange', s=150, zorder=5, edgecolors='white', linewidth=2)
                    
                    # 添加最新数值标签
                    ax2.annotate(f'{latest["北向资金"]:.1f}亿', 
                                xy=(latest['交易日期'], latest['北向资金']),
                                xytext=(10, 15), textcoords='offset points',
                                bbox=dict(boxstyle='round,pad=0.4', facecolor='orange', alpha=0.8),
                                fontsize=11, fontweight='bold', color='white',
                                ha='left', va='bottom')
        
        ax2.set_title(f'北向资金流入内地趋势 (最近{days}天)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('交易日期', fontsize=12)
        ax2.set_ylabel('资金净流入 (亿元)', fontsize=12)
        ax2.grid(True, alpha=0.3, linestyle='--')
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        output_file = os.path.join(self.output_dir, f"capital_flow_trends_{days}days.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        logger.info(f"资金流向趋势图已保存: {output_file}")
        
        plt.show()
    
    def create_combined_trends(self):
        """创建综合趋势图"""
        logger.info("正在创建综合趋势分析...")
        
        # 绘制HIBOR趋势
        print("\n📈 正在生成HIBOR利率趋势图...")
        self.plot_hibor_trends(days=60)
        
        # 绘制资金流向趋势
        print("\n💰 正在生成资金流向趋势图...")
        self.plot_capital_flow_trends(days=30)
        
        print(f"\n✅ 所有趋势图已生成完成！")
        print(f"📁 图表保存位置: {self.output_dir}/")

def main():
    """主函数"""
    print("=" * 60)
    print("香港金融市场趋势可视化")
    print("=" * 60)
    
    visualizer = TrendVisualizer()
    
    # 创建综合趋势分析
    visualizer.create_combined_trends()

if __name__ == "__main__":
    main()
